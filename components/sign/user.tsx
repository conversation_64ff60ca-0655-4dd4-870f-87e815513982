"use client";

import * as React from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON>etT<PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";

import { Link } from "@/i18n/routing";
import { User } from "@/types/user";
import { signOut } from "next-auth/react";
import { useTranslations } from "next-intl";
import { NavItem } from "@/types/blocks/base";
import { useModalStore } from "@/stores/modalStore";
import { useIsMobile } from "@/hooks/use-mobile";

export default function SignUser({ user }: { user: User }) {
  const t = useTranslations();
  const { openModal } = useModalStore();
  const isMobile = useIsMobile();

  const menuItems: NavItem[] = [
    {
      title: t("user.my_orders"),
      onClick: () => openModal('my-orders', {}),
    },
    {
      title: t("my_credits.title"),
      onClick: () => openModal('my-credits', {}),
    },
    {
      title: t("api_keys.title"),
      onClick: () => openModal('api-keys', {}),
    },
    {
      title: t("user.admin_system"),
      url: "/admin/users",
    },
    {
      title: t("user.sign_out"),
      onClick: () => signOut(),
    },
  ];

  // 移动端使用 Sheet
  if (isMobile) {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <Avatar className="cursor-pointer">
            <AvatarImage src={user.avatar_url} alt={user.nickname} />
            <AvatarFallback>{user.nickname}</AvatarFallback>
          </Avatar>
        </SheetTrigger>
        <SheetContent className="w-80">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={user.avatar_url} alt={user.nickname} />
                <AvatarFallback>{user.nickname}</AvatarFallback>
              </Avatar>
              <div className="text-left">
                <div className="font-semibold">{user.nickname}</div>
                <div className="text-sm text-muted-foreground">{user.email}</div>
              </div>
            </SheetTitle>
          </SheetHeader>
          <div className="mt-6 flex flex-col gap-2">
            {menuItems.map((item, index) => (
              <React.Fragment key={index}>
                {item.url ? (
                  <Link
                    href={item.url as any}
                    target={item.target}
                    className="flex items-center gap-3 rounded-lg px-3 py-3 text-left transition-colors hover:bg-accent hover:text-accent-foreground"
                  >
                    <span className="font-medium">{item.title}</span>
                  </Link>
                ) : (
                  <button
                    onClick={item.onClick}
                    className="flex items-center gap-3 rounded-lg px-3 py-3 text-left transition-colors hover:bg-accent hover:text-accent-foreground"
                  >
                    <span className="font-medium">{item.title}</span>
                  </button>
                )}
                {index < menuItems.length - 1 && <Separator />}
              </React.Fragment>
            ))}
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // 桌面端使用 DropdownMenu
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer">
          <AvatarImage src={user.avatar_url} alt={user.nickname} />
          <AvatarFallback>{user.nickname}</AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="mx-4 bg-background">
        <DropdownMenuItem className="flex justify-center cursor-default">
          <span className="font-semibold">{user.nickname}</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        {menuItems.map((item, index) => (
          <React.Fragment key={index}>
            <DropdownMenuItem
              key={index}
              className="flex justify-center cursor-pointer"
            >
              {item.url ? (
                <Link href={item.url as any} target={item.target}>
                  {item.title}
                </Link>
              ) : (
                <button onClick={item.onClick}>{item.title}</button>
              )}
            </DropdownMenuItem>
            {index !== menuItems.length - 1 && <DropdownMenuSeparator />}
          </React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
