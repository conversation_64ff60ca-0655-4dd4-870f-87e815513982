'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useEffect, useState } from "react";
import { format } from "date-fns";
import { AIModelUsage } from "@/types/ai-model";
import { parseWorkResult, parseWorkPrompt } from "@/models/works";
import { Eye, Download, Copy, RefreshCw } from "lucide-react";
import Image from "next/image";

interface MyWorksModalProps {
  locale?: string;
}

export default function MyWorksModal({}: MyWorksModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const [works, setWorks] = useState<AIModelUsage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState(0);
  const [models, setModels] = useState<Record<string, any>>({});

  const loadWorks = async (pageNum: number = 1, append: boolean = false) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/ai/usage?type=usage&page=${pageNum}&limit=20`);
      const result = await response.json() as any;

      if (result.code === 0 && result.data) {
        const worksData = result.data.records || [];

        if (append) {
          setWorks(prev => [...prev, ...worksData]);
        } else {
          setWorks(worksData);
        }

        setTotal(result.data.total || worksData.length);
        setHasMore(worksData.length === 20); // 如果返回的数据等于limit，说明可能还有更多
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Failed to load works:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadModels = async () => {
    try {
      const response = await fetch('/api/ai/models');
      const result = await response.json() as any;
      if (result.code === 0 && result.data?.models) {
        const modelMap = result.data.models.reduce((acc: Record<string, any>, model: any) => {
          acc[model.model_id] = model;
          return acc;
        }, {});
        setModels(modelMap);
      }
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  useEffect(() => {
    loadWorks();
    loadModels();
  }, []);

  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadWorks(page + 1, true);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      failed: 'destructive',
      pending: 'secondary',
      cancelled: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    );
  };

  const getModelTypeBadge = (modelId: string) => {
    const model = models[modelId];
    const type = model?.model_type || 'text';
    const colors: Record<string, string> = {
      text: 'bg-blue-100 text-blue-800',
      image: 'bg-green-100 text-green-800',
      video: 'bg-purple-100 text-purple-800',
      multimodal: 'bg-orange-100 text-orange-800'
    };

    return (
      <Badge className={colors[type] || colors.text}>
        {type}
      </Badge>
    );
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderWorkResult = (work: AIModelUsage) => {
    const result = parseWorkResult(work);
    
    if (!result) {
      return <span className="text-muted-foreground text-sm">No result available</span>;
    }

    switch (result.type) {
      case 'text':
        return (
          <div className="space-y-2">
            <p className="text-sm line-clamp-3">{result.content}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(result.content)}
              className="h-6 px-2"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
          </div>
        );
      
      case 'image':
        return (
          <div className="space-y-2">
            <div className="relative w-full h-32 bg-gray-100 rounded-md overflow-hidden">
              <Image
                src={result.content}
                alt="Generated image"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 300px"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(result.content, '_blank')}
                className="h-6 px-2"
              >
                <Eye className="h-3 w-3 mr-1" />
                View
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = result.content;
                  link.download = `image-${work.task_id}.png`;
                  link.click();
                }}
                className="h-6 px-2"
              >
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            </div>
          </div>
        );
      
      case 'video':
        return (
          <div className="space-y-2">
            <video
              src={result.content}
              controls
              className="w-full h-32 bg-gray-100 rounded-md"
              preload="metadata"
            />
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(result.content, '_blank')}
                className="h-6 px-2"
              >
                <Eye className="h-3 w-3 mr-1" />
                View
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = result.content;
                  link.download = `video-${work.task_id}.mp4`;
                  link.click();
                }}
                className="h-6 px-2"
              >
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            </div>
          </div>
        );
      
      default:
        return <span className="text-muted-foreground text-sm">Unknown result type</span>;
    }
  };

  const content = (
    <div className="space-y-4">
      {isLoading && works.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading...</span>
        </div>
      ) : works.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No AI model usage records found.</p>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Total: {total} records
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadWorks()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
          
          <div className="space-y-4">
            {works.map((work) => (
              <Card key={work.id} className="w-full">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="space-y-1">
                      <CardTitle className="text-base">{work.model_id}</CardTitle>
                      <div className="flex gap-2">
                        {getStatusBadge(work.status)}
                        {getModelTypeBadge(work.model_id)}
                        {work.provider && (
                          <Badge variant="outline">{work.provider}</Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      <div>{work.credits_consumed} credits</div>
                      <div>{format(new Date(work.created_at!), "MM/dd HH:mm")}</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* 用户输入的提示词 */}
                  <div>
                    <h4 className="text-sm font-medium mb-1">Prompt:</h4>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {parseWorkPrompt(work) || 'No prompt available'}
                    </p>
                  </div>
                  
                  {/* 生成结果 */}
                  {work.status === 'success' && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Result:</h4>
                      {renderWorkResult(work)}
                    </div>
                  )}
                  
                  {/* 错误信息 */}
                  {work.status === 'failed' && work.error_reason && (
                    <div>
                      <h4 className="text-sm font-medium mb-1 text-red-600">Error:</h4>
                      <p className="text-sm text-red-600">{work.error_reason}</p>
                      {work.error_detail && (
                        <p className="text-xs text-muted-foreground mt-1">{work.error_detail}</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
          
          {hasMore && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={loadMore}
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>My Works</DialogTitle>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent className="max-h-[95vh]">
        <DrawerHeader className="text-left">
          <DrawerTitle>My Works</DrawerTitle>
        </DrawerHeader>
        <div className="px-4 overflow-y-auto flex-1">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              Close
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
