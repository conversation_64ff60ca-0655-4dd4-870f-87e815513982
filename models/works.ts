import { AIModelUsage } from "@/types/ai-model";
import { getSupabaseClient } from "@/models/db";

export interface WorksQueryOptions {
  page?: number;
  limit?: number;
  status?: 'pending' | 'success' | 'failed' | 'cancelled';
  provider?: string;
}

export interface WorksQueryResult {
  data: AIModelUsage[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

/**
 * 获取用户的AI模型使用记录
 */
export async function getUserWorks(
  user_uuid: string,
  options: WorksQueryOptions = {}
): Promise<WorksQueryResult> {
  const supabase = getSupabaseClient();
  const {
    page = 1,
    limit = 20,
    status,
    provider
  } = options;

  let query = supabase
    .from("ai_model_usage")
    .select("*", { count: "exact" })
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false });

  // 添加过滤条件
  if (status) {
    query = query.eq("status", status);
  }
  if (provider) {
    query = query.eq("provider", provider);
  }

  // 分页
  const from = (page - 1) * limit;
  const to = from + limit - 1;
  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch user works: ${error.message}`);
  }

  return {
    data: data || [],
    total: count || 0,
    page,
    limit,
    hasMore: (count || 0) > page * limit
  };
}

/**
 * 根据任务ID获取单个AI模型使用记录
 */
export async function getWorkByTaskId(
  task_id: string,
  user_uuid: string
): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("task_id", task_id)
    .eq("user_uuid", user_uuid)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // 记录不存在
    }
    throw new Error(`Failed to fetch work: ${error.message}`);
  }

  return data;
}

/**
 * 获取用户的使用统计信息
 */
export async function getUserWorksStats(user_uuid: string) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("status, credits_consumed, model_id, provider")
    .eq("user_uuid", user_uuid);

  if (error) {
    throw new Error(`Failed to fetch user stats: ${error.message}`);
  }

  const stats = {
    total: data.length,
    success: data.filter(item => item.status === 'success').length,
    failed: data.filter(item => item.status === 'failed').length,
    pending: data.filter(item => item.status === 'pending').length,
    totalCredits: data.reduce((sum, item) => sum + (item.credits_consumed || 0), 0),
    modelTypes: {} as Record<string, number>,
    providers: {} as Record<string, number>
  };

  // 统计模型类型和提供商使用情况
  data.forEach(item => {
    if (item.model_id) {
      stats.modelTypes[item.model_id] = (stats.modelTypes[item.model_id] || 0) + 1;
    }
    if (item.provider) {
      stats.providers[item.provider] = (stats.providers[item.provider] || 0) + 1;
    }
  });

  return stats;
}

/**
 * 解析响应数据，提取实际生成的内容
 */
export function parseWorkResult(work: AIModelUsage) {
  if (!work.response_data) {
    return null;
  }

  try {
    const responseData = typeof work.response_data === 'string' 
      ? JSON.parse(work.response_data) 
      : work.response_data;

    // 文字模型结果
    if (responseData.result?.text) {
      return {
        type: 'text',
        content: responseData.result.text
      };
    }

    // 图片模型结果
    if (responseData.url) {
      return {
        type: 'image',
        content: responseData.url,
        width: responseData.width,
        height: responseData.height
      };
    }

    // 视频模型结果
    if (responseData.video_url || responseData.videoUrl) {
      return {
        type: 'video',
        content: responseData.video_url || responseData.videoUrl
      };
    }

    // 其他格式的图片结果
    if (responseData.results && Array.isArray(responseData.results) && responseData.results.length > 0) {
      return {
        type: 'image',
        content: responseData.results[0].url,
        width: responseData.results[0].width,
        height: responseData.results[0].height
      };
    }

    return null;
  } catch (error) {
    console.error('Failed to parse work result:', error);
    return null;
  }
}

/**
 * 解析请求参数，提取用户输入的提示词
 */
export function parseWorkPrompt(work: AIModelUsage): string {
  if (!work.request_params) {
    return '';
  }

  try {
    const requestParams = typeof work.request_params === 'string' 
      ? JSON.parse(work.request_params) 
      : work.request_params;

    // 直接的 prompt 字段
    if (requestParams.prompt) {
      return requestParams.prompt;
    }

    // 文字模型的 messages 格式
    if (requestParams.messages && Array.isArray(requestParams.messages)) {
      const userMessage = requestParams.messages.find((msg: any) => msg.role === 'user');
      return userMessage?.content || '';
    }

    return '';
  } catch (error) {
    console.error('Failed to parse work prompt:', error);
    return '';
  }
}

/**
 * 获取模型类型（从模型配置中获取准确类型）
 */
export function getModelType(modelId: string): 'text' | 'image' | 'video' | 'multimodal' {
  // 在客户端环境中，我们无法直接导入服务端的模型管理器
  // 这个函数应该在客户端组件中被替换为从 API 获取模型信息
  // 这里提供一个基本的回退逻辑
  const lowerModelId = modelId.toLowerCase();

  if (lowerModelId.includes('flux') || lowerModelId.includes('dalle') || lowerModelId.includes('midjourney')) {
    return 'image';
  }

  if (lowerModelId.includes('video') || lowerModelId.includes('runway') || lowerModelId.includes('pika')) {
    return 'video';
  }

  if (lowerModelId.includes('gemini') && lowerModelId.includes('vision')) {
    return 'multimodal';
  }

  if (lowerModelId.includes('gpt') || lowerModelId.includes('claude') || lowerModelId.includes('gemini')) {
    return 'text';
  }

  return 'text'; // 默认为文本类型
}
